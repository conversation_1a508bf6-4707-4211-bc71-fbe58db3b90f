import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Input, Popup } from "semantic-ui-react";
import { openModal } from "../../../../app/common/modals/modalSlice";
import {
  searchFilter,
  downloadAllDocumentsAsPdf,
} from "../../../../app/common/util/util";
import DocTransActiveList from "./DocTransActiveList";
import { useMediaQuery } from "react-responsive";
import { toast } from "react-toastify";

export default function DocTransActiveDashboard() {
  const dispatch = useDispatch();
  const { docsTransActive, docsTransArchived, docsTransShared } = useSelector(
    (state) => state.doc
  );
  const { transaction } = useSelector((state) => state.transaction);
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [searchTerms, setSearchTerms] = useState("");
  const [downloadingAll, setDownloadingAll] = useState(false);
  const docs = searchFilter(docsTransActive?.docs, searchTerms);
  const docsArchived = searchFilter(docsTransArchived?.docs, searchTerms);
  const docsShared = searchFilter(docsTransShared?.docs, searchTerms);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  // Get all documents for download (active + archived + shared)
  const allDocs = [
    ...(docsTransActive?.docs || []),
    ...(docsTransArchived?.docs || []),
    ...(docsTransShared?.docs || []),
  ];

  // Check if there are non-PDF documents to determine button text
  const hasNonPdfDocs = allDocs.some(
    (doc) => doc.filetype && doc.filetype !== "pdf"
  );
  const downloadButtonText = hasNonPdfDocs
    ? "Download All as ZIP"
    : "Download All as PDF";

  async function handleDownloadAll() {
    if (allDocs.length === 0) {
      toast.error("No documents available to download");
      return;
    }

    setDownloadingAll(true);
    try {
      await downloadAllDocumentsAsPdf(allDocs, transaction, currentUserProfile);
      const successMessage = hasNonPdfDocs
        ? "All documents downloaded as ZIP file successfully!"
        : "All documents downloaded as merged PDF successfully!";
      toast.success(successMessage);
    } catch (error) {
      console.error("Error downloading all documents:", error);
      toast.error(error.message || "Failed to download documents");
    } finally {
      setDownloadingAll(false);
    }
  }

  return (
    <div className="secondary-page-wrapper">
      <Grid stackable>
        <Grid.Column computer={8}>
          <Input
            type="text"
            fluid
            placeholder="Search by document name or status"
            value={searchTerms}
            onChange={(e) => setSearchTerms(e.target.value)}
          ></Input>
        </Grid.Column>
        <Grid.Column width={8}>
          <div className={isMobile ? null : "float-right"}>
            <Popup
              content={
                hasNonPdfDocs
                  ? "Downloads all PDFs merged into one file plus individual non-PDF files in a ZIP archive"
                  : "Downloads all PDF documents merged into a single PDF file"
              }
              trigger={
                <Button
                  icon="download"
                  size="small"
                  className={isMobile ? "fluid bottom margin" : "right margin"}
                  onClick={handleDownloadAll}
                  content={downloadButtonText}
                  color="teal"
                  loading={downloadingAll}
                  disabled={downloadingAll || allDocs.length === 0}
                />
              }
            />
            <Button
              to="#"
              icon="plus"
              size="small"
              className={isMobile ? "fluid" : null}
              onClick={() =>
                dispatch(
                  openModal({
                    modalType: "DocAddDocumentsModal",
                  })
                )
              }
              content="Add Documents"
              color="blue"
            />
          </div>
        </Grid.Column>

        <Grid.Column computer={16}>
          {docsArchived?.length > 0 && (
            <Header className="zero bottom margin">Active</Header>
          )}
          {docs?.length > 0 ? (
            <DocTransActiveList
              docs={docs}
              column={docsTransActive.column || "updatedAt"}
              direction={docsTransActive.direction || "descending"}
            />
          ) : (
            <p>There are no active documents</p>
          )}
        </Grid.Column>
        {docsShared?.length > 0 && (
          <Grid.Column computer={16}>
            <Header className="zero bottom margin">Shared</Header>
            <DocTransActiveList
              docs={docsShared}
              column={docsTransShared.column || "updatedAt"}
              direction={docsTransShared.direction || "descending"}
              shared={true}
            />
          </Grid.Column>
        )}
        {docsArchived?.length > 0 && (
          <Grid.Column computer={16}>
            <Header className="zero bottom margin">Archived</Header>
            <DocTransActiveList
              docs={docsArchived}
              column={docsTransArchived.column || "updatedAt"}
              direction={docsTransArchived.direction || "descending"}
              archived={true}
            />
          </Grid.Column>
        )}
      </Grid>
    </div>
  );
}
