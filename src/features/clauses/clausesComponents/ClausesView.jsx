import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Header,
  Segment,
  Tab,
  Input,
} from "semantic-ui-react";
import { useMediaQuery } from "react-responsive";
import LoadingComponent from "../../../app/layout/LoadingComponent";
import { closeModal } from "../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { searchFilter } from "../../../app/common/util/util";

// Component for expandable text display
function ExpandableText({ text, maxLines = 2 }) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => setIsExpanded(!isExpanded);

  const textStyle = {
    whiteSpace: "pre-line",
    overflow: "hidden",
    display: "-webkit-box",
    WebkitLineClamp: isExpanded ? "none" : maxLines,
    WebkitBoxOrient: "vertical",
    lineHeight: "1.4em",
    maxHeight: isExpanded ? "none" : `${maxLines * 1.4}em`
  };

  // Check if text is long enough to need expansion
  const needsExpansion = (text && text.split('\n').length > maxLines) ||
                        (text && text.length > 100); // rough estimate for 2 lines

  return (
    <div>
      <div style={textStyle}>
        {text}
      </div>
      {needsExpansion && (
        <Button
          basic
          size="mini"
          onClick={toggleExpanded}
          style={{ marginTop: "0.5em", padding: "0.3em 0.6em" }}
        >
          {isExpanded ? "Show Less" : "Show More"}
        </Button>
      )}
    </div>
  );
}

export default function ClausesView() {
  const dispatch = useDispatch();
  const { clauses } = useSelector((state) => state.clauses);
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [searchTerms, setSearchTerms] = useState("");
  const [managerSearchTerms, setManagerSearchTerms] = useState("");
  const [userSearchTerms, setUserSearchTerms] = useState("");

  const legalClauses = clauses?.filter((clause) => clause.type === "legal");
  const clausesFilteredOnSearch = searchFilter(legalClauses, searchTerms);

  // Manager clauses are user type clauses that belong to the current user's manager
  const managerClauses = clauses?.filter(
    (clause) =>
      clause.type === "user" &&
      clause.userId === currentUserProfile?.managerId &&
      clause.userId !== currentUserProfile?.userId
  );
  const managerClausesFilteredOnSearch = searchFilter(
    managerClauses,
    managerSearchTerms
  );

  // User clauses are user type clauses that belong to the current user
  const userClauses = clauses?.filter(
    (clause) =>
      clause.type === "user" && clause.userId === currentUserProfile?.userId
  );
  const userClausesFilteredOnSearch = searchFilter(
    userClauses,
    userSearchTerms
  );
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  // Check if user is an agent with a manager to show Manager tab
  const showManagerTab =
    currentUserProfile?.role === "agent" &&
    currentUserProfile?.managerId &&
    managerClauses?.length > 0;

  if (!clauses) {
    return <LoadingComponent />;
  }

  // Build panes array dynamically
  const panes = [
    {
      menuItem: "Legal",
      render: () => (
        <Tab.Pane>
          <Segment>
            <Input
              type="text"
              fluid
              placeholder="Search by clause name, description, or category"
              value={searchTerms}
              label="Search"
              onChange={(e) => setSearchTerms(e.target.value)}
            ></Input>
          </Segment>
          <Segment style={{ overflow: "auto", maxHeight: 600 }}>
            <Grid stackable>
              {legalClauses?.length === 0 && (
                <Grid.Row className="small top padding">
                  <Grid.Column width={3}>
                    <b>Clause</b>
                  </Grid.Column>
                  <Grid.Column width={13}>
                    Reach out to your brokerage <NAME_EMAIL>
                    their legal clauses to be used by users within your
                    brokerage.
                  </Grid.Column>
                </Grid.Row>
              )}
              {clausesFilteredOnSearch &&
                clausesFilteredOnSearch?.length !== 0 &&
                clausesFilteredOnSearch.map((clause) => (
                  <Grid.Row key={clause.id} className="small top padding">
                    <Grid.Column width={3}>
                      <b>{clause.title}</b>
                    </Grid.Column>
                    <Grid.Column width={13}>
                      <ExpandableText text={clause.text} />
                    </Grid.Column>
                  </Grid.Row>
                ))}
            </Grid>
          </Segment>
        </Tab.Pane>
      ),
    },
  ];

  // Add Manager tab if user is an agent with a manager and manager has clauses
  if (showManagerTab) {
    panes.splice(1, 0, {
      menuItem: "Manager",
      render: () => (
        <Tab.Pane>
          <Segment>
            <Input
              type="text"
              fluid
              placeholder="Search by clause name, description, or category"
              value={managerSearchTerms}
              label="Search"
              onChange={(e) => setManagerSearchTerms(e.target.value)}
            ></Input>
          </Segment>
          <Segment style={{ overflow: "auto", maxHeight: 600 }}>
            <Grid stackable>
              {managerClauses?.length === 0 && (
                <Grid.Row className="small top padding">
                  <Grid.Column width={3}>
                    <b>Clause</b>
                  </Grid.Column>
                  <Grid.Column width={13}>
                    No manager clauses available.
                  </Grid.Column>
                </Grid.Row>
              )}
              {managerClausesFilteredOnSearch &&
                managerClausesFilteredOnSearch?.length !== 0 &&
                managerClausesFilteredOnSearch.map((clause) => (
                  <Grid.Row key={clause.id} className="small top padding">
                    <Grid.Column width={3}>
                      <b>{clause.title}</b>
                    </Grid.Column>
                    <Grid.Column width={13}>
                      <ExpandableText text={clause.text} />
                    </Grid.Column>
                  </Grid.Row>
                ))}
            </Grid>
          </Segment>
        </Tab.Pane>
      ),
    });
  }

  // Add User tab
  panes.push({
    menuItem: "User",
    render: () => (
      <Tab.Pane>
        <Segment>
          <Input
            type="text"
            fluid
            placeholder="Search by clause name, description, or category"
            value={userSearchTerms}
            label="Search"
            onChange={(e) => setUserSearchTerms(e.target.value)}
          ></Input>
        </Segment>
        <Segment style={{ overflow: "auto", maxHeight: 600 }}>
          <Grid stackable>
            {userClauses?.length === 0 && (
              <Grid.Row className="small top padding">
                <Grid.Column width={3}>
                  <b>Clause</b>
                </Grid.Column>
                <Grid.Column width={13}>
                  You can add your own clauses by closing this window and going
                  to the menu in the upper right corner and clicking "My
                  Clauses"
                </Grid.Column>
              </Grid.Row>
            )}
            {userClausesFilteredOnSearch &&
              userClausesFilteredOnSearch?.length !== 0 &&
              userClausesFilteredOnSearch.map((clause) => (
                <Grid.Row key={clause.id} className="small top padding">
                  <Grid.Column width={3}>
                    <b>{clause.title}</b>
                  </Grid.Column>
                  <Grid.Column width={13}>
                    <ExpandableText text={clause.text} />
                  </Grid.Column>
                </Grid.Row>
              ))}
          </Grid>
        </Segment>
      </Tab.Pane>
    ),
  });

  return (
    <ModalWrapper>
      <Segment clearing>
        <div className="medium horizontal margin small top margin">
          <Header size="large" color="blue">
            {showManagerTab
              ? "Legal, Manager & User Clauses"
              : "Legal & User Clauses"}
          </Header>
          <Divider />
          To add a clause to your document: 1) Highlight the text below and copy
          it 2) Close this window 3) Paste the copied text into desired section
          of the document
          <Divider hidden />
          <Tab panes={panes} />
          <br />
          <Button
            onClick={() =>
              dispatch(
                closeModal({
                  modalType: "ClausesView",
                })
              )
            }
            type="button"
            floated={isMobile ? null : "right"}
            content="Close"
            className={isMobile ? "fluid medium bottom margin" : null}
          />
        </div>
      </Segment>
    </ModalWrapper>
  );
}
