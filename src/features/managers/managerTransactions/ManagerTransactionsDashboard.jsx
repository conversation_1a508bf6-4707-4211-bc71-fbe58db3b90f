import React, { useState } from "react";
import { useSelector } from "react-redux";
import { Grid, Button, Table, Input, Dropdown, Message, MessageHeader, Image } from "semantic-ui-react";
import { Link } from "react-router-dom";
import { addDays, startOfDay } from "date-fns";
import _ from "lodash";
import ManagerTransactionsListItem from "./ManagerTransactionsListItem";
import { searchFilter } from "../../../app/common/util/util";

export default function ManagerTransactionsDashboard() {
  const [searchTerms, setSearchTerms] = useState("");
  const [selectedDays, setSelectedDays] = useState(7);
  const { currentUserProfile } = useSelector((state) => state.profile);

  const daysOptions = [
    { key: 3, text: "3 days", value: 3 },
    { key: 7, text: "7 days", value: 7 },
    { key: 14, text: "14 days", value: 14 },
    { key: 31, text: "31 days", value: 31 },
  ];

  const {
    // transActiveForManager,
    transUnderContractForManager,
    // transActiveListingForManager,
    // transActiveBuyerForManager,
    transClosedForManager,
  } = useSelector((state) => state.transaction);
  const filteredTransactions = transUnderContractForManager.filter(
    (transaction) =>
      transaction.closingDateTime &&
      transaction.closingDateTime >= startOfDay(new Date()) &&
      transaction.closingDateTime <
        startOfDay(addDays(new Date(), selectedDays))
  );
  const searchedTransactions = searchFilter(filteredTransactions, searchTerms);

  const sortedTransactions = _.orderBy(
    searchedTransactions,
    "closingDateTime",
    "asc"
  );

  let closedTransactionsByAgent = {};
  transClosedForManager.forEach((transaction) => {
    if (
      transaction.agentProfile?.firstName &&
      transaction.agentProfile?.lastName
    ) {
      let salesPrice = 0;
      if (transaction.salesPrice) {
        salesPrice = parseFloat(transaction.salesPrice.replace(/[$,]/g, ""));
      }
      const agentName = `${transaction.agentProfile?.firstName} ${transaction.agentProfile?.lastName}`;
      if (closedTransactionsByAgent[agentName]) {
        closedTransactionsByAgent[agentName].count += 1;
        closedTransactionsByAgent[agentName].totalSalesPrice += salesPrice;
      } else {
        closedTransactionsByAgent[agentName] = {
          count: 1,
          totalSalesPrice: salesPrice,
        };
      }
    }
  });

  return (
     <>
              {currentUserProfile?.isTester === true && (
                <div className="text-center">
                  <Message color="blue" className="zero margin">
                    <MessageHeader>
                      This is a test account. No emails will be sent out when sharing or
                      sending documents for signing.
                    </MessageHeader>
                  </Message>
                </div>
              )}
        
              {!currentUserProfile?.isTester &&
                currentUserProfile?.type === "user" &&
                currentUserProfile?.managerDetails?.hasAccessToManagerTransactionLists &&
                currentUserProfile?.payments &&
                new Date(
                  Math.max(
                    ...currentUserProfile?.payments.map(
                      (item) => new Date(item.dateExpires.seconds * 1000)
                    )
                  )
                ) < new Date() && (
                  <div className="text-center">
                    <Message color="blue" className="zero margin">
                      <MessageHeader>
        
                                 <center><Image src="/assets/logo-original-with-text-75H.png"  style={{align: 'center'}} /></center> 
                        
                        <br/>Thank you for using TransActioner!
                        <br />
                        <h2>Your account has expired.</h2>
                        <br /> &nbsp; <br />
                        Please pay either a monthly subscription or a one-time annual payment to continue to use TransActioner.
                        <br /> &nbsp; <br />
                        <a
                          href="https://buy.stripe.com/7sI3cB7ZUg3L6TC4gh"
                          rel="noopener noreferrer"
                          target="_blank"
                        >
                          Click <u>here</u> to Pay $199 for 1 year.
                        </a>
                        <br /> &nbsp; <br />
                        <a
                          href="https://buy.stripe.com/3cs7sR2FAbNv3Hq3ce"
                          rel="noopener noreferrer"
                          target="_blank"
                        >
                          Click <u>here</u> to Subscribe for $19/month.
                        </a>
                        <br /> &nbsp; <br />
                        <i>
                          <small>
                            Changes will be reflected within 24 hours after payment.
                          </small>
                        </i>
                      </MessageHeader>
                     
                    </Message>
                  </div>
                )}
    <div className="main-page-wrapper">
      <>
        <Grid stackable className="large bottom margin"></Grid>
        <Grid>
          <Grid.Row>
            <Grid.Column
              computer={16}
              className="large top margin small bottom margin"
            >
              <h1
                className="zero bottom margin"
                style={{ position: "absolute", bottom: "0" }}
              >
                Manager Transactions
              </h1>
            </Grid.Column>
          </Grid.Row>
          <Grid.Row>
            <Grid.Column computer={4}>
              <Input
                type="text"
                fluid
                placeholder="Search"
                value={searchTerms}
                onChange={(e) => setSearchTerms(e.target.value)}
              ></Input>
            </Grid.Column>

            <Grid.Column computer={3}>
              <Dropdown
                fluid
                selection
                options={daysOptions}
                value={selectedDays}
                onChange={(e, { value }) => setSelectedDays(value)}
                placeholder="Select days"
              />
            </Grid.Column>

            <Grid.Column computer={3} tablet={4}>
              <Button.Group fluid size="small">
                <Button active as={Link} to="/managerUpcomingClosings">
                  Upcoming Closings
                </Button>
                <Button as={Link} to={`/managerRecentClosings/`}>
                  Recent Closings
                </Button>
                <Button as={Link} to={`/managerNewListings/`}>
                  New Listings
                </Button>
                <Button as={Link} to={`/managerNewBuyers/`}>
                  New Buyers
                </Button>
              </Button.Group>
            </Grid.Column>
          </Grid.Row>
          <Grid.Row>
            <Grid.Column
              computer={16}
              className="large top margin small bottom margin"
            >
              <h2
                className="zero bottom margin"
                style={{ position: "absolute", bottom: "0" }}
              >
                Upcoming Closings
              </h2>
            </Grid.Column>
          </Grid.Row>
          {currentUserProfile?.managerDetails
            ?.hasAccessToManagerTransactionLists ? (
            <Grid.Row>
              <Table compact>
                <Table.Header className="mobile hidden">
                  <Table.Row className="small-header">
                    <Table.HeaderCell></Table.HeaderCell>
                    <Table.HeaderCell>Closing Date</Table.HeaderCell>
                    <Table.HeaderCell>Agent</Table.HeaderCell>
                    <Table.HeaderCell>
                      Client&nbsp;&nbsp;(Primary)
                    </Table.HeaderCell>
                    <Table.HeaderCell>
                      Client&nbsp;&nbsp;(Secondary)
                    </Table.HeaderCell>
                    <Table.HeaderCell>Representing</Table.HeaderCell>
                    <Table.HeaderCell>Address</Table.HeaderCell>
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {sortedTransactions.map((transaction) => (
                    <ManagerTransactionsListItem
                      transaction={transaction}
                      key={transaction.id}
                      useDate={transaction.closingDateTime}
                    />
                  ))}
                </Table.Body>
              </Table>
            </Grid.Row>
          ) : (
            <Grid.Row>
              <p>
                Contact TransActioner to subscribe to Manager Transaction Lists.
              </p>
            </Grid.Row>
          )}
        </Grid>
      </>
    </div>
    </>
  );
}
