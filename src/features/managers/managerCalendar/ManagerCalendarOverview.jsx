import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { <PERSON>rid, <PERSON>ton, Message, MessageHeader, Image } from "semantic-ui-react";
// import { TaskAllCalendarWeekly } from "../../tasks/taskAll/TaskAllCalendarWeekly";
import { addDays, isSameDay, startOfWeek } from "date-fns";
import LoadingComponent from "../../../app/layout/LoadingComponent";
import { ManagerCalendar } from "./ManagerCalendar";
import { ManagerCalendarFullWeek } from "./ManagerCalendarFullWeek";

export default function ManagerCalendarOverview() {
  const { transActiveForManager, transClosedForManager } = useSelector(
    (state) => state.transaction
  );
  const { tasksAllWithDateUpcomingForManager } = useSelector(
    (state) => state.task
  );
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [tasksForDisplay, setTasksForDisplay] = useState([{}, {}, {}, {}]);
  const [tasksForFullWeek, setTasksForFullWeek] = useState([{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]);
  const [loading, setLoading] = useState(false);

  // Use user's calendar preference as default, fallback to "Two Weeks" if not set
  const defaultCalendarView = currentUserProfile?.calendarManager === "4days" ? false : true;
  const [showFullWeek, setShowFullWeek] = useState(defaultCalendarView);
  const transActive = transActiveForManager;

  // Update the calendar view when user profile changes
  useEffect(() => {
    if (currentUserProfile?.calendarManager) {
      const preferredView = currentUserProfile.calendarManager === "4days" ? false : true;
      setShowFullWeek(preferredView);
    }
  }, [currentUserProfile]);

  useEffect(() => {
    if (tasksAllWithDateUpcomingForManager.length > 0) {
      let tasksByDay = [];
      [0, 1, 2, 3].forEach((day) => {
        tasksByDay[day] = tasksAllWithDateUpcomingForManager.filter((task) =>
          isSameDay(task.end, addDays(new Date(), day - 1))
        );
      });
      let dayDisplay = [{}, {}, {}, {}];
      tasksByDay.forEach((day, index) => {
        const dayIndex = index;
        day.forEach((task) => {
          if (dayDisplay[dayIndex]?.[task.transactionId]?.["tasks"]) {
            dayDisplay[dayIndex][task.transactionId]["tasks"].push(task);
          } else {
            let transaction = transActive.filter(
              (trans) => trans.id === task.transactionId
            );
            if (transaction?.length > 0) {
              transaction = transaction[0];
            }
            if (!transaction || !transaction.id || transaction?.length === 0) {
              transaction = transClosedForManager.filter(
                (trans) => trans.id === task.transactionId
              );
              if (transaction?.length > 0) {
                transaction = transaction[0];
              }
            }

            dayDisplay[dayIndex][task.transactionId] = {
              address:
                transaction?.address?.street ||
                task.transactionTitle ||
                transaction?.title ||
                transaction?.client?.lastName ||
                "",
              agentName: transaction?.agentProfile
                ? transaction.agentProfile.firstName +
                  " " +
                  transaction.agentProfile.lastName
                : transaction?.agentName,
              agentRepresents: transaction?.agentRepresents,
              title: transaction?.title,
              transactionId: task.transactionId,
              closingDateTime: transaction?.closingDateTime,
              tasks: [task],
            };
          }
        });
      });
      setTasksForDisplay(dayDisplay);

      // Generate full week view (Current week + Next week = 14 days)
      const startOfCurrentWeek = startOfWeek(new Date(), { weekStartsOn: 0 });
      let weekTasksByDay = [];
      [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13].forEach((day) => {
        weekTasksByDay[day] = tasksAllWithDateUpcomingForManager.filter((task) =>
          isSameDay(task.end, addDays(startOfCurrentWeek, day))
        );
      });
      let weekDayDisplay = [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}];
      weekTasksByDay.forEach((day, index) => {
        const dayIndex = index;
        day.forEach((task) => {
          if (weekDayDisplay[dayIndex]?.[task.transactionId]?.["tasks"]) {
            weekDayDisplay[dayIndex][task.transactionId]["tasks"].push(task);
          } else {
            let transaction = transActive.filter(
              (trans) => trans.id === task.transactionId
            );
            if (transaction?.length > 0) {
              transaction = transaction[0];
            }
            if (!transaction || !transaction.id || transaction?.length === 0) {
              transaction = transClosedForManager.filter(
                (trans) => trans.id === task.transactionId
              );
              if (transaction?.length > 0) {
                transaction = transaction[0];
              }
            }

            weekDayDisplay[dayIndex][task.transactionId] = {
              address:
                transaction?.address?.street ||
                task.transactionTitle ||
                transaction?.title ||
                transaction?.client?.lastName ||
                "",
              agentName: transaction?.agentProfile
                ? transaction.agentProfile.firstName +
                  " " +
                  transaction.agentProfile.lastName
                : transaction?.agentName,
              agentRepresents: transaction?.agentRepresents,
              title: transaction?.title,
              transactionId: task.transactionId,
              closingDateTime: transaction?.closingDateTime,
              tasks: [task],
            };
          }
        });
      });
      setTasksForFullWeek(weekDayDisplay);
    }
    setLoading(true);
  }, [tasksAllWithDateUpcomingForManager, transActive, transClosedForManager]);

  if (!loading) return <LoadingComponent content="Loading details..." />;

  return (
    <>
          {currentUserProfile?.isTester === true && (
            <div className="text-center">
              <Message color="blue" className="zero margin">
                <MessageHeader>
                  This is a test account. No emails will be sent out when sharing or
                  sending documents for signing.
                </MessageHeader>
              </Message>
            </div>
          )}
    
          {!currentUserProfile?.isTester &&
            currentUserProfile?.type === "user" &&
            currentUserProfile?.managerDetails?.hasAccessToCalendarDashboard &&
            currentUserProfile?.payments &&
            new Date(
              Math.max(
                ...currentUserProfile?.payments.map(
                  (item) => new Date(item.dateExpires.seconds * 1000)
                )
              )
            ) < new Date() && (
              <div className="text-center">
                <Message color="blue" className="zero margin">
                  <MessageHeader>
    
                             <center><Image src="/assets/logo-original-with-text-75H.png"  style={{align: 'center'}} /></center> 
                    
                    <br/>Thank you for using TransActioner!
                    <br />
                    <h2>Your account has expired.</h2>
                    <br /> &nbsp; <br />
                    Please pay either a monthly subscription or a one-time annual payment to continue to use TransActioner.
                    <br /> &nbsp; <br />
                    <a
                      href="https://buy.stripe.com/7sI3cB7ZUg3L6TC4gh"
                      rel="noopener noreferrer"
                      target="_blank"
                    >
                      Click <u>here</u> to Pay $199 for 1 year.
                    </a>
                    <br /> &nbsp; <br />
                    <a
                      href="https://buy.stripe.com/3cs7sR2FAbNv3Hq3ce"
                      rel="noopener noreferrer"
                      target="_blank"
                    >
                      Click <u>here</u> to Subscribe for $19/month.
                    </a>
                    <br /> &nbsp; <br />
                    <i>
                      <small>
                        Changes will be reflected within 24 hours after payment.
                      </small>
                    </i>
                  </MessageHeader>
                 
                </Message>
              </div>
            )}
    <div className="main-page-wrapper">
      {currentUserProfile?.managerDetails?.hasAccessToCalendarDashboard ? (
        <Grid className="medium bottom margin">
          <Grid.Column computer={16}>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "36px",
              }}
            >
              <h3 style={{ margin: 0 }}>Calendar</h3>
              <Button.Group size="small">
                <Button
                  active={!showFullWeek}
                  onClick={() => setShowFullWeek(false)}
                >
                  4 Days
                </Button>
                <Button
                  active={showFullWeek}
                  onClick={() => setShowFullWeek(true)}
                >
                  Two Weeks
                </Button>
              </Button.Group>
            </div>
            <div className="small horizontal padding">
              {showFullWeek ? (
                <ManagerCalendarFullWeek tasksByDay={tasksForFullWeek} />
              ) : (
                <ManagerCalendar tasksByDay={tasksForDisplay} />
              )}
            </div>
          </Grid.Column>
        </Grid>
      ) : (
        <p>
          Contact TransActioner to subscribe to manager's calendar dashboard.
        </p>
      )}
    </div>
    </>
  );
}
